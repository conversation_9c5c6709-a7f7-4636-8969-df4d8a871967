"use client";
import React from "react";
import ToolBar from "../components/ToolBar";
import { secondaryFont } from "@/constants/fonts";
import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";

const Header = () => {
  const pathname = usePathname();

  const navItems = [
    { name: "About Us", path: "/about-us" },
    { name: "Contact Us", path: "/contact" },
    // { name: "Video Blogs", path: "/video-blogs" },
    // { name: "Blogs", path: "/blogs" },
  ];

  return (
    <motion.header
      className="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      

      {/* Main header */}
      <div className="container mx-auto px-3 sm:px-4 md:px-6">
        <div className="flex items-center justify-between h-14 sm:h-16 md:h-20">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <motion.div
              className={`${secondaryFont} text-lg sm:text-xl md:text-2xl font-bold relative`}
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <span className="text-accent">Chinioti</span>{" "}
              <span className="text-gray-800 hidden xs:inline">Wooden Art</span>
              <span className="text-gray-800 xs:hidden">WA</span>
              <motion.div
                className="absolute -bottom-1 left-0 h-0.5 bg-accent/30"
                initial={{ width: "0%" }}
                whileHover={{ width: "100%" }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
          </Link>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`relative text-sm font-medium transition-colors duration-200 ${
                  pathname === item.path
                    ? "text-accent"
                    : "text-gray-700 hover:text-accent"
                }`}
              >
                {item.name}
                {pathname === item.path && (
                  <motion.div
                    className="absolute -bottom-1 left-0 right-0 h-0.5 bg-accent"
                    layoutId="activeTab"
                  />
                )}
              </Link>
            ))}
          </nav>

          {/* Right side - Shop Now & Toolbar */}
          <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-4">
            <Link
              href="/products"
              className="hidden md:inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-accent text-white text-xs md:text-sm font-medium rounded-md hover:bg-accent/90 transition-colors duration-200"
            >
              Shop Now
            </Link>
            <ToolBar />
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-gray-200">
          <nav className="flex items-center justify-around py-2.5 sm:py-3 px-2">
            {navItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`text-xs sm:text-sm font-medium transition-colors duration-200 px-2 py-1 rounded-md min-w-0 text-center ${
                  pathname === item.path
                    ? "text-accent bg-accent/10"
                    : "text-gray-700 hover:text-accent hover:bg-accent/5"
                }`}
              >
                <span className="truncate">{item.name}</span>
              </Link>
            ))}
            <Link
              href="/products"
              className="text-xs sm:text-sm font-medium text-accent bg-accent/10 px-2 py-1 rounded-md hover:bg-accent/20 transition-colors"
            >
              Shop Now
            </Link>
          </nav>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
